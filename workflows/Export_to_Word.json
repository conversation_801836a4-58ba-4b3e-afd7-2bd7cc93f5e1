{"name": "Export to Word", "nodes": [{"parameters": {"mode": "runOnceForEachItem", "jsCode": "// const docx = require('docx');\n// const Document = docx.Document;\n// const Paragraph = docx.Paragraph;\n// const TextRun = docx.TextRun;\n// const Packer = docx.Packer;\n\n// const fs = require('fs')\n\n// // Even shorter with async/await\n// async function generateDocAsync(doc, filename) {\n//   try {\n//     const buffer = await Packer.toBuffer(doc);\n//     console.log(filename)\n//     fs.writeFileSync(filename, buffer);\n//     return true;\n//   } catch (error) {\n//     console.log(\"error at write file: \" + error)\n//     return false;\n//   }\n// }\n\n// function textToWordDoc(text, filename = 'document', options = {}) {\n//   // Default options\n//   const defaultOptions = {\n//     fontSize: 12,\n//     fontFamily: 'Calibri',\n//     lineSpacing: 1.15,\n//     titleText: '',\n//     titleSize: 16,\n//     titleBold: true\n//   };\n  \n//   // Merge default options with provided options\n//   const mergedOptions = { ...defaultOptions, ...options };\n  \n//   // Create a new document\n//   const doc = new Document({\n//     sections: [{\n//       properties: {},\n//       children: []\n//     }]\n//   });\n  \n//   // Add title if provided\n//   if (mergedOptions.titleText) {\n//     doc.addSection({\n//       children: [\n//         new Paragraph({\n//           children: [\n//             new TextRun({\n//               text: mergedOptions.titleText,\n//               bold: mergedOptions.titleBold,\n//               size: mergedOptions.titleSize * 2, // Size in half-points\n//               font: mergedOptions.fontFamily\n//             })\n//           ],\n//           spacing: {\n//             after: 200 // Space after the title\n//           }\n//         })\n//       ]\n//     });\n//   }\n  \n//   // Split the text into paragraphs\n//   const paragraphs = text.split(/\\n+/);\n//   const paragraphDocs = []; // Create an array to hold paragraph objects\n    \n//   // Process each paragraph\n//   paragraphs.forEach(paragraphText => {\n//     if (paragraphText.trim() === '') return; // Skip empty paragraphs\n      \n//     // Create a new paragraph and push it to the array\n//     paragraphDocs.push(\n//       new Paragraph({\n//         children: [\n//           new TextRun({\n//             text: paragraphText,\n//             size: mergedOptions.fontSize * 2, // Size in half-points\n//             font: mergedOptions.fontFamily\n//           })\n//         ],\n//         spacing: {\n//           line: Math.round(mergedOptions.lineSpacing * 240) // Line spacing in twips\n//         }\n//       })\n//     );\n//   });\n\n//   doc.addSection({\n//     children: paragraphDocs\n//   })\n  \n//   return generateDocAsync(doc, filename);\n// }\n\n// return textToWordDoc($json.text, \"/app/Output/\" + $json.title + \".docx\")\n//   .then(result => {\n//     return {\n//       json: {\n//         completed: Boolean(result)\n//       }\n//     };\n//   })\n//   .catch(error => {\n//     return {\n//       json: {\n//         completed: false,\n//         error: error.message\n//       }\n//     };\n//   });\n\nconst docx = require('docx');\nconst { Document, Paragraph, TextRun, HeadingLevel, AlignmentType, Packer } = docx;\nconst fs = require('fs');\n\n// Helper function to write the document to a file\nasync function generateDocAsync(doc, filename) {\n  try {\n    const buffer = await Packer.toBuffer(doc);\n    fs.writeFileSync(filename, buffer);\n    return true;\n  } catch (error) {\n    console.log(\"error at write file: \" + error);\n    return false;\n  }\n}\n\nfunction textToWordDoc(text, filename = 'document', options = {}) {\n  // Default options\n  const defaultOptions = {\n    fontSize: 12,\n    fontFamily: 'Calibri',\n    lineSpacing: 1.15,\n    titleText: '',\n    titleSize: 16,\n    titleBold: true\n  };\n  \n  // Merge default options with provided options\n  const mergedOptions = { ...defaultOptions, ...options };\n    \n  // Array to hold all paragraphs\n  const paragraphDocs = [];\n  \n  // // Add title if provided from the options\n  // if (mergedOptions.titleText) {\n  //   paragraphDocs.push(\n  //     new Paragraph({\n  //       children: [\n  //         new TextRun({\n  //           text: mergedOptions.titleText,\n  //           bold: mergedOptions.titleBold,\n  //           size: mergedOptions.titleSize * 2, // Size in half-points\n  //           font: mergedOptions.fontFamily\n  //         })\n  //       ],\n  //       spacing: {\n  //         after: 200 // Space after the title\n  //       }\n  //     })\n  //   );\n  // }\n  \n  // Split the text into paragraphs\n  const paragraphs = text.split(/\\n+/);\n  \n  // Process each paragraph\n  paragraphs.forEach(paragraphText => {\n    if (paragraphText.trim() === '') return; // Skip empty paragraphs\n    \n    // Check for markdown headers\n    if (paragraphText.startsWith('###')) {\n      // Create a heading paragraph\n      paragraphDocs.push(\n        new Paragraph({\n          text: paragraphText.replace('###', '').trim(),\n          heading: HeadingLevel.HEADING_3,\n          spacing: {\n            before: 240,\n            after: 120\n          }\n        })\n      );\n      return;\n    }\n    \n    // Check for bullet points\n    if (paragraphText.startsWith('-')) {\n      // Create a bullet point paragraph\n      paragraphDocs.push(\n        new Paragraph({\n          text: paragraphText.substring(1).trim(),\n          bullet: {\n            level: 0\n          },\n          spacing: {\n            line: Math.round(mergedOptions.lineSpacing * 240)\n          }\n        })\n      );\n      return;\n    }\n    \n    // Check for bold text within paragraphs (text between ** **)\n    if (paragraphText.includes('**')) {\n      const children = [];\n      let remainingText = paragraphText;\n      let boldStart = remainingText.indexOf('**');\n      \n      while (boldStart !== -1) {\n        // Add text before the bold section\n        if (boldStart > 0) {\n          children.push(\n            new TextRun({\n              text: remainingText.substring(0, boldStart),\n              size: mergedOptions.fontSize * 2,\n              font: mergedOptions.fontFamily\n            })\n          );\n        }\n        \n        // Find the end of the bold section\n        const boldEnd = remainingText.indexOf('**', boldStart + 2);\n        if (boldEnd === -1) break; // No closing ** found\n        \n        // Add the bold text\n        children.push(\n          new TextRun({\n            text: remainingText.substring(boldStart + 2, boldEnd),\n            bold: true,\n            size: mergedOptions.fontSize * 2,\n            font: mergedOptions.fontFamily\n          })\n        );\n        \n        // Update the remaining text\n        remainingText = remainingText.substring(boldEnd + 2);\n        boldStart = remainingText.indexOf('**');\n      }\n      \n      // Add any remaining text\n      if (remainingText) {\n        children.push(\n          new TextRun({\n            text: remainingText,\n            size: mergedOptions.fontSize * 2,\n            font: mergedOptions.fontFamily\n          })\n        );\n      }\n      \n      // Create a paragraph with the mixed text\n      paragraphDocs.push(\n        new Paragraph({\n          children: children,\n          spacing: {\n            line: Math.round(mergedOptions.lineSpacing * 240)\n          }\n        })\n      );\n    } else {\n      // Regular paragraph without special formatting\n      paragraphDocs.push(\n        new Paragraph({\n          children: [\n            new TextRun({\n              text: paragraphText,\n              size: mergedOptions.fontSize * 2,\n              font: mergedOptions.fontFamily\n            })\n          ],\n          spacing: {\n            line: Math.round(mergedOptions.lineSpacing * 240)\n          }\n        })\n      );\n    }\n  });\n  \n  // Create a new document\n  const doc = new Document({\n    sections: [{\n      properties: {},\n      children: paragraphDocs\n    }]\n  });\n  \n  return generateDocAsync(doc, `${filename}.docx`);\n}\n\n// Example usage:\n// Extract title from the first line if it contains ** (bold)\nfunction processDocumentWithFormatting(text, filename) {\n  const lines = text.split('\\n');\n  let title = '';\n  let content = text;\n  \n  // Check if the first line is a title (bold)\n  if (lines[0].startsWith('**') && lines[0].endsWith('**')) {\n    title = lines[0].replace(/\\*\\*/g, '');\n    content = lines.slice(1).join('\\n');\n  }\n  \n  return textToWordDoc(content, filename, {\n    titleText: title,\n    titleSize: 18,\n    titleBold: true,\n    fontSize: 11,\n    fontFamily: 'Calibri',\n    lineSpacing: 1.15\n  });\n}\n\nconst filename = \"/app/internal/Output/\" + $json.title\n\nreturn processDocumentWithFormatting($json.text, filename)\n  .then(result => {\n    return {\n      json: {\n        completed: Boolean(result),\n        filename: filename,\n      }\n    };\n  })\n  .catch(error => {\n    return {\n      json: {\n        completed: false,\n        filename: filename,\n        error: error.message\n      }\n    };\n  });"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [200, 0], "id": "a0067717-bf86-4008-8677-7b6353c7e2b5", "name": "Code"}, {"parameters": {"workflowInputs": {"values": [{"name": "text"}, {"name": "title"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [0, 0], "id": "ea93bcc5-4cf2-4f6e-a31e-127cdcce2997", "name": "When Executed by Another Workflow"}], "pinData": {}, "connections": {"When Executed by Another Workflow": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "c4aeff15-b469-44c9-91a5-8a24d0f7ae66", "meta": {"instanceId": "dfbe0a8fb074e911d00a16a102748458e0e2909756ec18adcfb1e5de930dd936"}, "id": "PtPBU055J9w64p3O", "tags": []}