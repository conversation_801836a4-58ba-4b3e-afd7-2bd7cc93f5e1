{"name": "Ingest Word Doc", "nodes": [{"parameters": {"workflowInputs": {"values": [{"name": "folder"}, {"name": "extension"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [0, 0], "id": "f7803055-0afc-4900-8d52-6f4b26f1bc37", "name": "When Executed by Another Workflow"}, {"parameters": {"mode": "runOnceForEachItem", "jsCode": "// Function Node: Read DOCX File\nconst mammoth = require('mammoth');\nconst fs = require('fs').promises;\n\nasync function readDocx(filePath) {\n    const dataBuffer = await fs.readFile(filePath);\n    const result = await mammoth.extractRawText({buffer: dataBuffer});\n    return result.value;\n}\n\n// Example: Read DOCX from file path\nconst filePath = $json.directory+\"/\"+$json.fileName;\nconst docxText = await readDocx(filePath);\n\nreturn {\n    json: {\n        name: $json.fileName,\n        text: docxText,\n    }\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [440, 0], "id": "36e0f196-a180-421e-89f1-30c308b01d08", "name": "Word Doc"}, {"parameters": {"fileSelector": "={{ $json.folder }}*.{{ $json.extension }}", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [220, 0], "id": "ee3135d2-5538-47e1-9be6-7d4494e4729e", "name": "Read/Write Files from Disk"}], "pinData": {}, "connections": {"When Executed by Another Workflow": {"main": [[{"node": "Read/Write Files from Disk", "type": "main", "index": 0}]]}, "Read/Write Files from Disk": {"main": [[{"node": "Word Doc", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "3c4c01ed-0b48-46be-bd3e-6e98bfd2d582", "meta": {"instanceId": "dfbe0a8fb074e911d00a16a102748458e0e2909756ec18adcfb1e5de930dd936"}, "id": "OUbGWzyneleP9rpw", "tags": []}