{"name": "Websearch", "nodes": [{"parameters": {"workflowInputs": {"values": [{"name": "Topic"}, {"name": "Search Amount", "type": "number"}, {"name": "Additional Keyword"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [1300, -280], "id": "d024f32c-da0e-48e9-bfd3-17f9af712359", "name": "When Executed by Another Workflow"}, {"parameters": {"fieldToSplitOut": "items", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [1740, -280], "id": "563eddd1-a2c4-477e-bb48-0805a28199b4", "name": "Split Out Items"}, {"parameters": {"url": "https://customsearch.googleapis.com/customsearch/v1", "sendQuery": true, "queryParameters": {"parameters": [{"name": "key", "value": "AIzaSyDBosFnqpIcw7ubDM4d3KmP9w0WIEOtnrI"}, {"name": "q", "value": "={{ $json.Topic }}, {{ $json['Additional Keyword'] }}"}, {"name": "cx", "value": "75518db8d1b544921"}, {"name": "dateRestrict", "value": "m6"}, {"name": "num", "value": "={{ $json['Search Amount'] }}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1520, -280], "id": "987912ba-c4b3-437b-999e-2e4b67d7c24e", "name": "Google Search"}, {"parameters": {"mode": "runOnceForEachItem", "jsCode": "var extractData = {\n  url: $input.item.json.link,\n  title: $input.item.json.title,\n}\n\nreturn extractData;"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1960, -280], "id": "e294879b-bb14-4627-b007-7abf72bedf0a", "name": "Code"}], "pinData": {}, "connections": {"Split Out Items": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "When Executed by Another Workflow": {"main": [[{"node": "Google Search", "type": "main", "index": 0}]]}, "Google Search": {"main": [[{"node": "Split Out Items", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "66726a16-6b94-4018-b0ac-ffc42ed2c505", "meta": {"templateCredsSetupCompleted": true, "instanceId": "dfbe0a8fb074e911d00a16a102748458e0e2909756ec18adcfb1e5de930dd936"}, "id": "hquwXuB7srwHm8tE", "tags": []}