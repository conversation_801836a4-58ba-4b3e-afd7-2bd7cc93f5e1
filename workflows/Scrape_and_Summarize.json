{"name": "Scrape and Summarize", "nodes": [{"parameters": {"promptType": "define", "text": "=You are going to look at some text data that has been scraped from the web. Please go thru them and generate a summary. \n\nPay specific attention to any problems that need solving pertaining to the topic at hand.\n\n{{ $json.combinedText }}", "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.8, "position": [440, 80], "id": "5e6660f7-d450-4029-a93c-2346a3b289d2", "name": "AI Summary"}, {"parameters": {"mode": "runOnceForEachItem", "jsCode": "const request = require('request')\nconst cheerio = require('cheerio')\n\nfunction ScrapePage(url) {\n    const headers = {\n        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',\n        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',\n        'Accept-Language': 'en-US,en;q=0.9',\n        // 'Accept-Encoding': 'gzip, deflate, br',\n        'Accept-Encoding': 'identity',\n        'Connection': 'keep-alive',\n        'Cache-Control': 'max-age=0',\n        'Sec-Ch-Ua': '\"Google Chrome\";v=\"121\", \"Not;A=Brand\";v=\"8\"',\n        'Sec-Ch-Ua-Mobile': '?0',\n        'Sec-Ch-Ua-Platform': '\"Windows\"',\n        'Sec-Fetch-Dest': 'document',\n        'Sec-Fetch-Mode': 'navigate',\n        'Sec-Fetch-Site': 'none',\n        'Sec-Fetch-User': '?1',\n        'Upgrade-Insecure-Requests': '1',\n        'Referer': 'https://www.google.com/'\n    }\n\n    return new Promise((resolve, reject) => {\n        request({\n            url: url,\n            headers: headers,\n            gzip: true, // Handle gzip compression\n            jar: true,   // Enable cookies\n            followRedirect: true // Follow redirects\n        }, (err, res, html) => {\n            if (err) {\n                reject(err);\n                return \"\";\n            }\n          \n            const $ = cheerio.load(html);\n          \n            // Initialize result object\n            const result = {\n                url: url,\n                statusCode: res.statusCode,\n                title: \"\",\n                description: \"\",\n                text: \"\"\n            };\n\n            // Extract title if requested\n            result.title = $('title').text().trim();\n            \n            // Extract meta description if requested\n            result.description = $('meta[name=\"description\"]').attr('content') || '';\n          \n            // // Remove script and style elements that might contain unwanted text\n            // $('script, style, noscript, iframe, svg, img, label, input, form').remove();\n            \n            // // Get all text from the body, removing excessive whitespace\n            // result.text = $('body').text()\n            //     .replace(/\\s+/g, ' ')\n            //     .trim();\n            \n            // resolve(result);\n\n            // Remove common noise elements\n            $('script, style, noscript, iframe, svg, img, label, input, form, footer, nav, header, aside').remove();\n            $('[id*=\"cookie\"], [id*=\"banner\"], [id*=\"popup\"], [id*=\"modal\"], [id*=\"newsletter\"], [class*=\"cookie\"], [class*=\"banner\"], [class*=\"popup\"], [class*=\"modal\"], [class*=\"newsletter\"]').remove();\n            $('[id*=\"ad-\"], [id*=\"ads-\"], [class*=\"ad-\"], [class*=\"ads-\"]').remove();\n            $('[id*=\"sidebar\"], [class*=\"sidebar\"]').remove();\n            $('[id*=\"comment\"], [class*=\"comment\"]').remove();\n            $('[id*=\"related\"], [class*=\"related\"]').remove();\n            \n            // Get all text from the body, removing excessive whitespace\n            let bodyText = $('body').text()\n                .replace(/\\s+/g, ' ')\n                .trim();\n                \n            // Remove common noise patterns (like \"Read more\", \"Share this\", etc.)\n            const noisePatterns = [\n                /Read more/gi,\n                /Share this/gi,\n                /Subscribe to/gi,\n                /Sign up for/gi,\n                /\\d+ comments/gi,\n                /Posted on \\w+/gi,\n                /Copyright © \\d+/gi\n            ];\n            \n            noisePatterns.forEach(pattern => {\n                bodyText = bodyText.replace(pattern, '');\n            });\n            \n            result.text = bodyText;\n            \n            resolve(result);\n        });\n    });\n}\n\nvar urlString = $input.item.json.url;\nvar result = await ScrapePage(urlString);\n\nvar resp = {\n  text: result,\n}\n\nreturn resp;"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-440, 80], "id": "c883afe5-9772-48d3-a296-495d20a86957", "name": "Scrape"}, {"parameters": {"jsCode": "// Get the items array from the previous node\nconst items = $input.all();\n\n// Extract the text field from each item and join them\n// Change \"textField\" to the actual name of your text field\nconst combinedText = items.map(item => item.json.text.text).join('\\n');\n\n// Return as a single item with the combined text\nreturn [{\n  json: {\n    combinedText: combinedText\n  }\n}];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [0, 0], "id": "bba89650-c53e-47c7-aa6f-7c866035f628", "name": "Combine"}, {"parameters": {"batchSize": 3, "options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-220, 80], "id": "6155781e-d206-4d2d-8f9a-34ea82dd3d49", "name": "Loop Over Items"}, {"parameters": {}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [220, 0], "id": "cc91ff28-8b33-45dc-89d0-4577a87e3490", "name": "Wait", "webhookId": "6e1b4ebc-23a2-4fd4-9291-defc37f9f5ac"}, {"parameters": {"jsCode": "const url = require('url');\n\n// Define your blacklist of blocked domains\nconst BLACKLIST = [\n  'facebook.com',\n  'twitter.com',\n  'x.com',\n  'instagram.com',\n  'reddit.com',\n  'youtube.com',\n  'medium.com'\n  // Add more domains as needed\n];\n\nfunction isBlacklisted(urlString) {\n  try {\n    // Parse the URL to extract the hostname and path\n    const parsedUrl = url.parse(urlString);\n    const hostname = parsedUrl.hostname;\n    const pathname = parsedUrl.pathname || '';\n    \n    // Check if the file has a .pdf extension\n    const isPdf = pathname.toLowerCase().endsWith('.pdf');\n    \n    // Check if the hostname or any of its parent domains are in the blacklist\n    const isBlacklistedDomain = BLACKLIST.some(domain => \n      hostname === domain || \n      hostname.endsWith('.' + domain)\n    );\n    \n    // Return true if either condition is met\n    return isBlacklistedDomain || isPdf;\n  } catch (error) {\n    console.log(error);\n    return true; // If we can't parse the URL, consider it blacklisted for safety\n  }\n}\n\n// Get all input items\nconst allItems = $input.all();\nconst filteredItems = []; \n\n// Loop through all items\nfor (let i = 0; i < allItems.length; i++) {\n  const item = allItems[i];\n  \n  try {\n    // Skip items with invalid structure or blacklisted URLs\n    if (item.json && item.json.url && !isBlacklisted(item.json.url)) {\n      // Remove any invalid top-level keys before adding to filtered items\n      const cleanItem = {\n        json: item.json\n      };\n      \n      // Only include binary if it exists\n      if (item.binary) {\n        cleanItem.binary = item.binary;\n      }\n      \n      filteredItems.push(cleanItem);\n    }\n  } catch (error) {\n    console.log(`Error processing item ${i}:`, error);\n    // Skip items that cause errors\n  }\n}\n\nreturn filteredItems;"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-660, 80], "id": "bc6465ef-3fe5-4efe-b631-b54b02f77419", "name": "Blacklist"}, {"parameters": {"inputSource": "passthrough"}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [-880, 80], "id": "28deadde-dd4c-48b0-b687-0ae7e3ceba7a", "name": "When Executed by Another Workflow"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [540, 300], "id": "5eac3fd6-2487-45bd-8bde-590efc3e36db", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "aFcWaKcTRaaP4L7Y", "name": "OpenAi account"}}}], "pinData": {}, "connections": {"AI Summary": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Scrape": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Combine": {"main": [[{"node": "Wait", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "Combine", "type": "main", "index": 0}]]}, "Wait": {"main": [[{"node": "AI Summary", "type": "main", "index": 0}]]}, "Blacklist": {"main": [[{"node": "Scrape", "type": "main", "index": 0}]]}, "When Executed by Another Workflow": {"main": [[{"node": "Blacklist", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Summary", "type": "ai_languageModel", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "168be86f-19ab-43c9-aac3-b442c46383ef", "meta": {"templateCredsSetupCompleted": true, "instanceId": "dfbe0a8fb074e911d00a16a102748458e0e2909756ec18adcfb1e5de930dd936"}, "id": "XLAJPnyMglmf6gGk", "tags": []}