{"name": "Automated Journal", "nodes": [{"parameters": {"formTitle": "Start Automated Journal", "formFields": {"values": [{"fieldLabel": "<PERSON><PERSON>", "requiredField": true}, {"fieldLabel": "Size of search", "fieldType": "number", "placeholder": "3", "requiredField": true}]}, "responseMode": "lastNode", "options": {}}, "type": "n8n-nodes-base.formTrigger", "typeVersion": 2.2, "position": [-720, 840], "id": "dc36ff3a-85f7-4377-ba96-75de07405913", "name": "On form submission", "webhookId": "5eefddd3-1d6b-418f-9a0d-5acf9cba6677"}, {"parameters": {"model": {"__rl": true, "value": "gpt-4o-mini", "mode": "list", "cachedResultName": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [3360, 440], "id": "e2722343-10c9-401a-a2dc-3e29ef267447", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "3ojSCFbf7RsyDJ3B", "name": "OpenAi account"}}}, {"parameters": {"promptType": "define", "text": "=Now, also generate 2 questions or terms I can use to search and research more about this topic: {{ $json['Chosen Topic'] }}", "hasOutputParser": true, "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.8, "position": [-500, 1680], "id": "8d87bf1c-8838-4c8a-bc78-b29c71a2108a", "name": "Search Term Generate"}, {"parameters": {"fieldToSplitOut": "output.search_questions, output.search_terms", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [-140, 1680], "id": "8a44c0aa-1549-4ead-ace2-4d8357586dbc", "name": "Split Out Items4"}, {"parameters": {"schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"search_questions\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"string\"\n      }\n    },\n    \"search_terms\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"string\"\n      }\n    }\n  }\n}\n"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [-420, 1900], "id": "82cb6dc0-dd8a-44c7-b810-680c6a6dfee8", "name": "Structured Output Parser"}, {"parameters": {"fieldToSplitOut": "['output.search_questions']", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [100, 1540], "id": "7f3e4234-8ac1-47a9-8bb5-8894e1e0415e", "name": "Split Out"}, {"parameters": {"fieldToSplitOut": "['output.search_terms']", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [100, 1740], "id": "c76a1a57-278b-4615-9160-0179a99004cc", "name": "Split Out1"}, {"parameters": {"promptType": "define", "text": "=You are representing Knovel Engineering, a local Singapore based startup that provides AI augemented solutions. Finally, write up a blog post, discussing the topic, and how a chosen solution is ideal. You will be provided the topic, several summarizations and a list of solutions to choose from. If there are no solutions that are ideal, then write an article describing the topic and the how we may be able to help. \n\nBe sure to include some SEO words into the article that can boost the score for it as well. Also make the article 800-1500 words long.\n\nHere is the topic:\n{{ $json['Chosen Topic'] }}\n\nHere are the solutions that you provide:\n{{ $json.solutionSummary }}\n\nHere are the summarizations of the topic:\n{{ $json.text }}\n\nThe format of the article is as follows:\n- Intro\n- The current trends\n- Challenges of the topic\n- Implications of the challenge to companies\n- How this challenge is mitigated by the chosen solution\n- Conclusion\n- End with an call to action \"Knovel Engineering: Your Trusted AI Solutions Partner\" and a paragraph describing how our company and our solutions are able to support potential readers\n\nAlso, follow this rules when writing:\n{{ $json.rules }}", "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.8, "position": [3340, 220], "id": "ceed631f-46f3-4082-b18b-78fd03c46b00", "name": "Blog Writer"}, {"parameters": {"aggregate": "aggregateAllItemData", "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [980, 300], "id": "46e3405a-b8d6-48e5-a956-e95f6f3a8bb7", "name": "Aggregate"}, {"parameters": {"workflowId": {"__rl": true, "value": "S3naUwljVKqNy9Oa", "mode": "list", "cachedResultName": "Websearch"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"Topic": "={{ $json['Chosen Topic'] }}", "Search Amount": "={{ $json[\"Size of search\"] }}", "Additional Keyword": "News"}, "matchingColumns": [], "schema": [{"id": "Topic", "displayName": "Topic", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}, {"id": "Search Amount", "displayName": "Search Amount", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "number"}, {"id": "Additional Keyword", "displayName": "Additional Keyword", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [320, 720], "id": "569b54bb-ac01-46f6-8204-9e3239b8a3da", "name": "Websearch - News"}, {"parameters": {"workflowId": {"__rl": true, "value": "S3naUwljVKqNy9Oa", "mode": "list", "cachedResultName": "Websearch"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"Topic": "={{ $json['Chosen Topic'] }}", "Search Amount": "={{ $json[\"Size of search\"] }}", "Additional Keyword": "Trends"}, "matchingColumns": [], "schema": [{"id": "Topic", "displayName": "Topic", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}, {"id": "Search Amount", "displayName": "Search Amount", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "number"}, {"id": "Additional Keyword", "displayName": "Additional Keyword", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [320, 920], "id": "776eeac5-bc41-42b7-9a1c-6402c4e80aeb", "name": "Websearch - Trends"}, {"parameters": {"workflowId": {"__rl": true, "value": "S3naUwljVKqNy9Oa", "mode": "list", "cachedResultName": "Websearch"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"Topic": "={{ $json['Chosen Topic'] }}", "Search Amount": "={{ $json[\"Size of search\"] }}", "Additional Keyword": "Impact"}, "matchingColumns": [], "schema": [{"id": "Topic", "displayName": "Topic", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}, {"id": "Search Amount", "displayName": "Search Amount", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "number"}, {"id": "Additional Keyword", "displayName": "Additional Keyword", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [320, 1120], "id": "b5970811-dc3a-4afe-8c0b-f7cbf6a860fd", "name": "Websearch - Impact"}, {"parameters": {"workflowId": {"__rl": true, "value": "S3naUwljVKqNy9Oa", "mode": "list", "cachedResultName": "Websearch"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"Topic": "={{ $json['Chosen Topic'] }}", "Search Amount": "={{ $json[\"Size of search\"] }}", "Additional Keyword": "Y Combinator"}, "matchingColumns": [], "schema": [{"id": "Topic", "displayName": "Topic", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}, {"id": "Search Amount", "displayName": "Search Amount", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "number"}, {"id": "Additional Keyword", "displayName": "Additional Keyword", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [320, 420], "id": "d9d10a51-8d07-40f4-a7ea-0f14cc640854", "name": "Websearch - Y Combinator"}, {"parameters": {"workflowId": {"__rl": true, "value": "S3naUwljVKqNy9Oa", "mode": "list", "cachedResultName": "Websearch"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"Topic": "={{ $json['Chosen Topic'] }}", "Search Amount": "={{ $json[\"Size of search\"] }}", "Additional Keyword": "The New Stack"}, "matchingColumns": [], "schema": [{"id": "Topic", "displayName": "Topic", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}, {"id": "Search Amount", "displayName": "Search Amount", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "number"}, {"id": "Additional Keyword", "displayName": "Additional Keyword", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [320, 220], "id": "1658e28f-e4f9-4d1c-ae4c-dc7f3d3a7495", "name": "Websearch - The New Stack"}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "Writing", "contextWindowLength": 10}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [3480, 440], "id": "4ae7eff2-525c-42ab-814b-b5914d991234", "name": "Simple Memory"}, {"parameters": {"jsCode": "// Loop over input items and add a new field called 'myNewField' to the JSON of each one\nvar combinedString = \"\";\nfor (const item of $input.first().json.solutions) {\n  combinedString += \"Name: \"\n  combinedString += item.name;\n  combinedString += \" | Text Description: \"\n  combinedString += item.text;\n  combinedString += \"\\n-------------------\\n\";\n}\n\nreturn [{\n  json: {\n    solutionSummary: combinedString\n  }\n}];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1860, 120], "id": "9f0a1484-c0ff-421d-a8b7-79a22635128b", "name": "Translate Solutions to JSON"}, {"parameters": {"mode": "runOnceForEachItem", "jsCode": "// Add a new field called 'myNewField' to the JSON of the item\nvar combinedStr = \"\";\nfor (const item of $json.data) {\n  combinedStr += item.output;\n  combinedStr += \"\\n\";\n}\n\nreturn {\n  json: {\n    combinedSummarisations: combinedStr\n  }\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1200, 300], "id": "cb92352a-d6ca-4f3e-85c2-27932e7ae45f", "name": "Combine Summarization Text"}, {"parameters": {"numberInputs": 8}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [760, 200], "id": "a5c71c7f-9412-4c88-804d-457c30be00de", "name": "<PERSON><PERSON>"}, {"parameters": {"workflowId": {"__rl": true, "value": "cIOGBmyKXvuAfOlY", "mode": "list", "cachedResultName": "Scrape and Summarize"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [540, 220], "id": "dc6021fc-5603-4ab0-88f6-9abbf1a9a5d8", "name": "Summarize - The New Stack"}, {"parameters": {"workflowId": {"__rl": true, "value": "cIOGBmyKXvuAfOlY", "mode": "list", "cachedResultName": "Scrape and Summarize"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [540, 420], "id": "5cd06a46-fa4d-4d95-a3b2-890b8a4dfc03", "name": "Summarize - Y Combinator"}, {"parameters": {"workflowId": {"__rl": true, "value": "cIOGBmyKXvuAfOlY", "mode": "list", "cachedResultName": "Scrape and Summarize"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [540, 720], "id": "6358313e-11dc-4b3f-956d-71b64a6b34f8", "name": "Summarize - News"}, {"parameters": {"workflowId": {"__rl": true, "value": "cIOGBmyKXvuAfOlY", "mode": "list", "cachedResultName": "Scrape and Summarize"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [540, 920], "id": "674f27a8-6437-4a19-8fdc-d59f1f02176b", "name": "Summarize - Trends"}, {"parameters": {"workflowId": {"__rl": true, "value": "cIOGBmyKXvuAfOlY", "mode": "list", "cachedResultName": "Scrape and Summarize"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [540, 1120], "id": "63118e1d-271f-4764-a5f9-8d30c5a8913b", "name": "Summarize - Impact"}, {"parameters": {"workflowId": {"__rl": true, "value": "S3naUwljVKqNy9Oa", "mode": "list", "cachedResultName": "Websearch"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"Topic": "={{ $('On form submission').item.json['Chosen Topic'] }}", "Search Amount": "={{ $('On form submission').item.json[\"Size of search\"] }}", "Additional Keyword": "={{ $json['[\\'output.search_questions\\']'] }}"}, "matchingColumns": [], "schema": [{"id": "Topic", "displayName": "Topic", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}, {"id": "Search Amount", "displayName": "Search Amount", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "number"}, {"id": "Additional Keyword", "displayName": "Additional Keyword", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}], "attemptToConvertTypes": false, "convertFieldsToString": true}, "mode": "each", "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [320, 1540], "id": "820a4882-3554-4891-b1d8-232b0b9f60f3", "name": "Websearch - Questions (Generated)"}, {"parameters": {"workflowId": {"__rl": true, "value": "cIOGBmyKXvuAfOlY", "mode": "list", "cachedResultName": "Scrape and Summarize"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [540, 1540], "id": "6238eb31-f1dd-4e30-9d7d-adb85441cafc", "name": "Summarize - Questions (Generated)"}, {"parameters": {"workflowId": {"__rl": true, "value": "cIOGBmyKXvuAfOlY", "mode": "list", "cachedResultName": "Scrape and Summarize"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [540, 1740], "id": "98b7b3c4-1eae-4acf-aa19-35301340b55f", "name": "Summarize - Search Terms (Generated)"}, {"parameters": {"workflowId": {"__rl": true, "value": "S3naUwljVKqNy9Oa", "mode": "list", "cachedResultName": "Websearch"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"Topic": "={{ $('On form submission').item.json['Chosen Topic'] }}", "Search Amount": "={{ $('On form submission').item.json[\"Size of search\"] }}", "Additional Keyword": "={{ $json['[\\'output.search_terms\\']'] }}"}, "matchingColumns": [], "schema": [{"id": "Topic", "displayName": "Topic", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}, {"id": "Search Amount", "displayName": "Search Amount", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "number"}, {"id": "Additional Keyword", "displayName": "Additional Keyword", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}], "attemptToConvertTypes": false, "convertFieldsToString": true}, "mode": "each", "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [320, 1740], "id": "d54df1ee-c73b-460c-bb7e-806f1ab81b25", "name": "Websearch - Search Terms (Generated)"}, {"parameters": {"promptType": "define", "text": "=Given this combined string of different summaries, create a complete summary for the provided topic. Make it descriptive and verbose enough so that this can be used as an input into anotehr AI tool to read and work upon\n\nTopic:  {{ $json['Chosen Topic'] }}\n\nCombined Summaries:\n{{ $json.combinedSummarisations }}"}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.6, "position": [2520, 300], "id": "33ac01d1-db4c-4ca7-a60d-505009b48484", "name": "Topic Summarization"}, {"parameters": {"workflowId": {"__rl": true, "value": "S3naUwljVKqNy9Oa", "mode": "list", "cachedResultName": "Websearch"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"Topic": "={{ $json['Chosen Topic'] }}", "Search Amount": "={{ $json[\"Size of search\"] }}", "Additional Keyword": "Challenges caused by it"}, "matchingColumns": [], "schema": [{"id": "Topic", "displayName": "Topic", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}, {"id": "Search Amount", "displayName": "Search Amount", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "number"}, {"id": "Additional Keyword", "displayName": "Additional Keyword", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [320, 1320], "id": "ebfe5363-ff90-4f79-b5f6-4b5a2a9e65b4", "name": "Websearch - Challenges"}, {"parameters": {"workflowId": {"__rl": true, "value": "cIOGBmyKXvuAfOlY", "mode": "list", "cachedResultName": "Scrape and Summarize"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [540, 1320], "id": "a3b3377e-df59-4d8d-b7bb-88f29a348323", "name": "Summarize - Challenges"}, {"parameters": {"workflowId": {"__rl": true, "value": "qT8z6kZsYtWz76U0", "mode": "list", "cachedResultName": "Export To Word"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"text": "={{ $json.output }}", "title": "={{ $('Merge (Rules)').item.json['Chosen Topic'] }}-{{ $now }}"}, "matchingColumns": [], "schema": [{"id": "text", "displayName": "text", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}, {"id": "title", "displayName": "title", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}], "attemptToConvertTypes": "=", "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [3720, 220], "id": "c4fd73b1-a7cc-4ce6-9e30-2fd992edd3cb", "name": "Output to Word Doc"}, {"parameters": {"workflowId": {"__rl": true, "value": "AL8Rg7JHPIQJcFvV", "mode": "list", "cachedResultName": "Ingest Word Doc"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"folder": "/app/internal/Solutions/", "extension": "docx"}, "matchingColumns": [], "schema": [{"id": "folder", "displayName": "folder", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}, {"id": "extension", "displayName": "extension", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [1420, 120], "id": "1d4e52cc-6a54-44fb-b8df-4575e29e0272", "name": "Read Solutions"}, {"parameters": {"jsCode": "const allOutput = []\nfor (const item of $input.all()) {\n  // Create a JSON with these fields\n  // Name\n  // Text\n\n  const solution = {\n    name: item.json.name,\n    text: item.json.text,\n  }\n  allOutput.push(solution)\n}\n\n// Return a list of JSON\nreturn [{\n  json: {\n    solutions: allOutput\n  }\n}];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1640, 120], "id": "14acc11e-e581-4a70-986f-71019f3e94b6", "name": "Parse Solution To JSON"}, {"parameters": {"workflowId": {"__rl": true, "value": "AL8Rg7JHPIQJcFvV", "mode": "list", "cachedResultName": "Ingest Word Doc"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"folder": "/app/internal/Config/", "extension": "docx"}, "matchingColumns": [], "schema": [{"id": "folder", "displayName": "folder", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}, {"id": "extension", "displayName": "extension", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [2300, 500], "id": "b50cf9fe-d153-4fcc-8d2a-cc7a448706f6", "name": "Read House Rules"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "5bebd6e3-40a8-4a01-8025-4be851fb3aa8", "leftValue": "={{ $json.name }}", "rightValue": "writer_rules", "operator": {"type": "string", "operation": "contains"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [2600, 500], "id": "d0c818f4-5292-431e-ad0b-e43976663709", "name": "If", "alwaysOutputData": true, "onError": "continueRegularOutput"}, {"parameters": {"assignments": {"assignments": [{"id": "2969e0d5-4f81-4999-912b-fc17a6214220", "name": "rules", "value": "={{ $json.text }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [2900, 500], "id": "ec791f5b-9752-4f77-8e4b-1066c9dcd5d3", "name": "Rules Field"}, {"parameters": {"mode": "combine", "combineBy": "combineByPosition", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [2300, 120], "id": "42ea1f91-4a42-41a3-b7e8-88894ae2920e", "name": "Merge (Summary + Topic)"}, {"parameters": {"mode": "combine", "combineBy": "combineByPosition", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [2080, 120], "id": "d86397a0-1ab9-4c4d-9692-c24f19efac75", "name": "Merge (Solution + Summary)"}, {"parameters": {"mode": "combine", "combineBy": "combineAll", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [3120, 220], "id": "5da00f7a-e594-4cb4-ad3f-cf40500ead4e", "name": "Merge (Rules)"}, {"parameters": {"mode": "combine", "combineBy": "combineAll", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [2900, 120], "id": "8eb98960-f518-4cc4-b99d-1a96c13e7c6f", "name": "Merge (Topic/Summary/Solution)"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "2d458091-be70-4b4a-85fb-d38fc93b6bbc", "leftValue": "={{ $json.completed }}", "rightValue": "", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [3940, 220], "id": "04b5e596-8863-4768-8560-798bd7b5d77c", "name": "If1"}, {"parameters": {"operation": "completion", "completionTitle": "Article failed to generate", "completionMessage": "Please try again.", "options": {}}, "type": "n8n-nodes-base.form", "typeVersion": 1, "position": [4160, 320], "id": "20d5ff3a-6afe-4355-b98a-104e5300664f", "name": "Failed Ending", "webhookId": "d1d273bd-3a8b-4747-8c23-fff7fbae2c43"}, {"parameters": {"fileSelector": "={{ $json.filename }}.docx", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [4160, 120], "id": "4f509159-9ebb-4f38-b496-c419cec956d4", "name": "Read/Write Files from Disk"}, {"parameters": {"operation": "completion", "respondWith": "returnBinary", "completionTitle": "=Article for {{ $('Merge (Rules)').item.json['Chosen Topic'] }} completed", "completionMessage": "You may also find the file in the output directory", "options": {}}, "type": "n8n-nodes-base.form", "typeVersion": 1, "position": [4380, 120], "id": "b114940e-7d9d-4183-b22e-c7b409b2771a", "name": "Success Ending", "webhookId": "3c1f1439-6576-4881-a781-2ddd716b4b88"}, {"parameters": {"content": "## Web Searching & Individual Summarization\n- Perform targeted google searches\n- Summarize results from each target search", "height": 2040, "width": 1860}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [200, -140], "id": "39eb294f-5301-4a6e-9db2-8f94494dbb41", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "## Ingest Solutions", "height": 220, "width": 700, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1340, 60], "id": "8095103c-0a41-4a3b-a65d-eb188a3d9458", "name": "Sticky Note2"}, {"parameters": {"content": "## Ingest writer's 'house' rules", "height": 240, "width": 760, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [2260, 420], "id": "a7419bb4-2bef-4656-b448-95859d9d8014", "name": "Sticky Note3"}, {"parameters": {"content": "## Handle output\n- Write to directory\n- Return created word doc", "height": 420, "width": 1200, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [3500, 60], "id": "5e1d108a-c5a1-4260-8462-01ec37307940", "name": "Sticky Note4"}, {"parameters": {"content": "## Data Preparation\n- Generate combined summary\n- Merge together summary of topic, solutions and house rules", "height": 480, "width": 1280, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [2080, -40], "id": "b9d01201-0d97-475e-ba5d-4d5fbc602e40", "name": "Sticky Note5"}, {"parameters": {"content": "## List of settings for sub-workflows\n\n### Read House Rules\nfolder: /app/internal/Config/\nextension: docx\n\n### Read Solutions\nfolder: /app/internal/Solutions/\nextension: docx\n\n### Output to Word Doc\ntext: {{ $json.output }}\ntitle: {{ $('Merge (Rules)').item.json['Chosen Topic'] }}-{{ $now }}", "height": 340, "width": 460, "color": 2}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [3500, -320], "id": "30a945b9-cb40-428e-b6a5-e3495b4a8c36", "name": "Sticky Note1"}], "pinData": {}, "connections": {"On form submission": {"main": [[{"node": "Websearch - News", "type": "main", "index": 0}, {"node": "Websearch - Trends", "type": "main", "index": 0}, {"node": "Websearch - Impact", "type": "main", "index": 0}, {"node": "Websearch - Y Combinator", "type": "main", "index": 0}, {"node": "Websearch - The New Stack", "type": "main", "index": 0}, {"node": "Search Term Generate", "type": "main", "index": 0}, {"node": "Merge (Summary + Topic)", "type": "main", "index": 1}, {"node": "Websearch - Challenges", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Search Term Generate", "type": "ai_languageModel", "index": 0}, {"node": "Blog Writer", "type": "ai_languageModel", "index": 0}, {"node": "Topic Summarization", "type": "ai_languageModel", "index": 0}]]}, "Search Term Generate": {"main": [[{"node": "Split Out Items4", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Search Term Generate", "type": "ai_outputParser", "index": 0}]]}, "Split Out Items4": {"main": [[{"node": "Split Out", "type": "main", "index": 0}, {"node": "Split Out1", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "Websearch - Questions (Generated)", "type": "main", "index": 0}]]}, "Split Out1": {"main": [[{"node": "Websearch - Search Terms (Generated)", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "Combine Summarization Text", "type": "main", "index": 0}]]}, "Websearch - Impact": {"main": [[{"node": "Summarize - Impact", "type": "main", "index": 0}]]}, "Websearch - Trends": {"main": [[{"node": "Summarize - Trends", "type": "main", "index": 0}]]}, "Websearch - News": {"main": [[{"node": "Summarize - News", "type": "main", "index": 0}]]}, "Websearch - Y Combinator": {"main": [[{"node": "Summarize - Y Combinator", "type": "main", "index": 0}]]}, "Websearch - The New Stack": {"main": [[{"node": "Summarize - The New Stack", "type": "main", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "Blog Writer", "type": "ai_memory", "index": 0}]]}, "Translate Solutions to JSON": {"main": [[{"node": "Merge (Solution + Summary)", "type": "main", "index": 0}]]}, "Combine Summarization Text": {"main": [[{"node": "Read Solutions", "type": "main", "index": 0}, {"node": "Read House Rules", "type": "main", "index": 0}, {"node": "Merge (Solution + Summary)", "type": "main", "index": 1}]]}, "Merge": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Summarize - The New Stack": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Summarize - Y Combinator": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Summarize - News": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 2}]]}, "Summarize - Trends": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 3}]]}, "Summarize - Impact": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 4}]]}, "Websearch - Questions (Generated)": {"main": [[{"node": "Summarize - Questions (Generated)", "type": "main", "index": 0}]]}, "Summarize - Questions (Generated)": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 6}]]}, "Summarize - Search Terms (Generated)": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 7}]]}, "Websearch - Search Terms (Generated)": {"main": [[{"node": "Summarize - Search Terms (Generated)", "type": "main", "index": 0}]]}, "Topic Summarization": {"main": [[{"node": "Merge (Topic/Summary/Solution)", "type": "main", "index": 1}]]}, "Websearch - Challenges": {"main": [[{"node": "Summarize - Challenges", "type": "main", "index": 0}]]}, "Summarize - Challenges": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 5}]]}, "Blog Writer": {"main": [[{"node": "Output to Word Doc", "type": "main", "index": 0}]]}, "Read Solutions": {"main": [[{"node": "Parse Solution To JSON", "type": "main", "index": 0}]]}, "Parse Solution To JSON": {"main": [[{"node": "Translate Solutions to JSON", "type": "main", "index": 0}]]}, "Read House Rules": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "Rules Field", "type": "main", "index": 0}]]}, "Rules Field": {"main": [[{"node": "Merge (Rules)", "type": "main", "index": 1}]]}, "Merge (Summary + Topic)": {"main": [[{"node": "Topic Summarization", "type": "main", "index": 0}, {"node": "Merge (Topic/Summary/Solution)", "type": "main", "index": 0}]]}, "Merge (Solution + Summary)": {"main": [[{"node": "Merge (Summary + Topic)", "type": "main", "index": 0}]]}, "Merge (Rules)": {"main": [[{"node": "Blog Writer", "type": "main", "index": 0}]]}, "Merge (Topic/Summary/Solution)": {"main": [[{"node": "Merge (Rules)", "type": "main", "index": 0}]]}, "Output to Word Doc": {"main": [[{"node": "If1", "type": "main", "index": 0}]]}, "If1": {"main": [[{"node": "Read/Write Files from Disk", "type": "main", "index": 0}], [{"node": "Failed Ending", "type": "main", "index": 0}]]}, "Read/Write Files from Disk": {"main": [[{"node": "Success Ending", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "da3feb96-be4d-4630-8fc9-2c3a473e7c00", "meta": {"templateCredsSetupCompleted": true, "instanceId": "5e45916734c344fa9e49381cdbbaac5754e436f6fd13673fa4a288280cf671cd"}, "id": "Om79upsfxZN7Uw5t", "tags": []}